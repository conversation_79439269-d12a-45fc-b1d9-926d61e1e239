// Test simple pour vérifier la logique de détermination des types de versements
// Ce fichier peut être utilisé pour tester la logique avant l'intégration

import 'package:flutter/material.dart';

class VersementTypeTest {
  // Reproduction de la logique de détermination du type
  static String determinerTypeVersement(Map<String, dynamic> versementData) {
    // Vérifier s'il y a des champs spécifiques qui indiquent le type
    if (versementData.containsKey('type_versement')) {
      return versementData['type_versement'].toString();
    }
    
    // Si il y a un transaction_id, c'est probablement un paiement mobile money
    if (versementData.containsKey('transaction_id') && 
        versementData['transaction_id'] != null && 
        versementData['transaction_id'].toString().isNotEmpty) {
      return 'mobile_money';
    }
    
    // Si il y a un champ indiquant un scan QR
    if (versementData.containsKey('qr_scan') && 
        versementData['qr_scan'] == true) {
      return 'scan';
    }
    
    // Logique basée sur d'autres critères
    String montantSaisi = versementData['montant_saisi']?.toString() ?? '0';
    String montantVers = versementData['montant_vers']?.toString() ?? '0';
    String monnaie = versementData['monnaie']?.toString() ?? '0';
    
    // Si pas de monnaie utilisée et montants égaux, probablement mobile money ou scan
    if (monnaie == '0' && montantSaisi == montantVers) {
      return 'mobile_money'; // Par défaut
    }
    
    // Sinon, c'est probablement un versement manuel
    return 'manuel';
  }

  static IconData getIconForVersementType(String? typeVersement) {
    switch (typeVersement) {
      case 'manuel':
        return Icons.payment;
      case 'mobile_money':
        return Icons.phone_android;
      case 'scan':
        return Icons.qr_code_scanner;
      default:
        return Icons.check_circle;
    }
  }

  static Color getColorForVersementType(String? typeVersement) {
    switch (typeVersement) {
      case 'manuel':
        return Colors.blue;
      case 'mobile_money':
        return Colors.orange;
      case 'scan':
        return Colors.teal;
      default:
        return Colors.green;
    }
  }

  static String getLabelForVersementType(String? typeVersement) {
    switch (typeVersement) {
      case 'manuel':
        return 'Manuel';
      case 'mobile_money':
        return 'Mobile Money';
      case 'scan':
        return 'Scan QR';
      default:
        return 'Standard';
    }
  }

  // Méthode de test
  static void runTests() {
    print('=== Tests de Détermination des Types de Versements ===\n');

    // Test 1: Versement avec type explicite
    var test1 = {
      'type_versement': 'manuel',
      'montant_saisi': '5000',
      'montant_vers': '5000',
      'monnaie': '1000'
    };
    var type1 = determinerTypeVersement(test1);
    print('Test 1 - Type explicite: $type1 (attendu: manuel)');
    print('Icône: ${getIconForVersementType(type1)}');
    print('Couleur: ${getColorForVersementType(type1)}');
    print('Label: ${getLabelForVersementType(type1)}\n');

    // Test 2: Versement avec transaction_id (mobile money)
    var test2 = {
      'transaction_id': 'TXN123456',
      'montant_saisi': '5000',
      'montant_vers': '5000',
      'monnaie': '0'
    };
    var type2 = determinerTypeVersement(test2);
    print('Test 2 - Avec transaction_id: $type2 (attendu: mobile_money)');
    print('Icône: ${getIconForVersementType(type2)}');
    print('Couleur: ${getColorForVersementType(type2)}');
    print('Label: ${getLabelForVersementType(type2)}\n');

    // Test 3: Versement par scan QR
    var test3 = {
      'qr_scan': true,
      'montant_saisi': '5000',
      'montant_vers': '5000',
      'monnaie': '0'
    };
    var type3 = determinerTypeVersement(test3);
    print('Test 3 - Scan QR: $type3 (attendu: scan)');
    print('Icône: ${getIconForVersementType(type3)}');
    print('Couleur: ${getColorForVersementType(type3)}');
    print('Label: ${getLabelForVersementType(type3)}\n');

    // Test 4: Versement manuel (avec monnaie)
    var test4 = {
      'montant_saisi': '5000',
      'montant_vers': '5000',
      'monnaie': '2000'
    };
    var type4 = determinerTypeVersement(test4);
    print('Test 4 - Manuel avec monnaie: $type4 (attendu: manuel)');
    print('Icône: ${getIconForVersementType(type4)}');
    print('Couleur: ${getColorForVersementType(type4)}');
    print('Label: ${getLabelForVersementType(type4)}\n');

    // Test 5: Versement sans monnaie (mobile money par défaut)
    var test5 = {
      'montant_saisi': '5000',
      'montant_vers': '5000',
      'monnaie': '0'
    };
    var type5 = determinerTypeVersement(test5);
    print('Test 5 - Sans monnaie: $type5 (attendu: mobile_money)');
    print('Icône: ${getIconForVersementType(type5)}');
    print('Couleur: ${getColorForVersementType(type5)}');
    print('Label: ${getLabelForVersementType(type5)}\n');

    print('=== Fin des Tests ===');
  }
}

// Pour exécuter les tests, décommentez la ligne suivante :
// void main() => VersementTypeTest.runTests();
